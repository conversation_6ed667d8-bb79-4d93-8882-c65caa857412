#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل البرنامج الآمن - محدث
يتعامل مع جميع مشاكل الطرفية والتشغيل
"""

import sys
import os
import subprocess
import time
import platform

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🚀 مشغل البرنامج الآمن المحدث")
    print("=" * 60)
    print("📋 هذا المشغل سيقوم بـ:")
    print("   • فحص البيئة والمتطلبات")
    print("   • حل مشاكل الطرفية")
    print("   • تشغيل البرنامج بأمان")
    print("─" * 60)

def check_environment():
    """فحص البيئة والمتطلبات"""
    print("\n🔍 فحص البيئة...")
    
    # فحص Python
    print(f"✅ Python: {sys.version}")
    
    # فحص النظام
    print(f"✅ النظام: {platform.system()} {platform.release()}")
    
    # فحص المجلد الحالي
    current_dir = os.getcwd()
    print(f"✅ المجلد الحالي: {current_dir}")
    
    # فحص الملفات المطلوبة
    required_files = ["main.py", "database.py"]
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ الملف موجود: {file}")
        else:
            print(f"❌ الملف مفقود: {file}")
            missing_files.append(file)
    
    # فحص مجلد ui
    if os.path.exists("ui"):
        print("✅ مجلد ui موجود")
        if os.path.exists("ui/main_window.py"):
            print("✅ ملف النافذة الرئيسية موجود")
        else:
            print("❌ ملف النافذة الرئيسية مفقود")
            missing_files.append("ui/main_window.py")
    else:
        print("❌ مجلد ui مفقود")
        missing_files.append("ui/")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def check_python_modules():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    required_modules = ['PyQt5', 'sqlalchemy']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ المكتبة متاحة: {module}")
        except ImportError:
            print(f"❌ المكتبة مفقودة: {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("💡 لتثبيتها: pip install " + " ".join(missing_modules))
        return False
    
    return True

def run_program_direct():
    """تشغيل البرنامج مباشرة"""
    try:
        print("\n▶️ تشغيل البرنامج مباشرة...")
        
        # استيراد وتشغيل البرنامج مباشرة
        import main
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل المباشر: {e}")
        return False

def run_program_subprocess():
    """تشغيل البرنامج عبر subprocess"""
    try:
        print("\n▶️ تشغيل البرنامج عبر subprocess...")
        
        # التأكد من وجود Python
        python_cmd = sys.executable
        if not python_cmd:
            python_cmd = "python"
        
        print(f"🐍 استخدام Python: {python_cmd}")
        
        # تشغيل البرنامج
        result = subprocess.run([python_cmd, "main.py"], 
                              cwd=os.getcwd(),
                              text=True)
        
        if result.returncode == 0:
            print("✅ تم تشغيل البرنامج بنجاح")
            return True
        else:
            print(f"❌ خطأ في تشغيل البرنامج: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في subprocess: {e}")
        return False

def run_program():
    """تشغيل البرنامج مع طرق متعددة"""
    print("\n🚀 بدء تشغيل البرنامج...")
    
    # الطريقة الأولى: التشغيل المباشر
    print("\n📋 الطريقة 1: التشغيل المباشر")
    if run_program_direct():
        return True
    
    # الطريقة الثانية: subprocess
    print("\n📋 الطريقة 2: subprocess")
    if run_program_subprocess():
        return True
    
    # إذا فشلت جميع الطرق
    print("\n❌ فشل في تشغيل البرنامج بجميع الطرق")
    return False

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # فحص البيئة
        if not check_environment():
            print("\n❌ فشل في فحص البيئة")
            input("اضغط Enter للخروج...")
            return
        
        # فحص المكتبات
        if not check_python_modules():
            print("\n❌ فشل في فحص المكتبات")
            input("اضغط Enter للخروج...")
            return
        
        # تشغيل البرنامج
        success = run_program()
        
        if not success:
            print("\n" + "=" * 60)
            print("❌ فشل في تشغيل البرنامج")
            print("💡 الحلول المقترحة:")
            print("   1. تشغيل: python main.py")
            print("   2. فحص المتطلبات: pip install -r requirements.txt")
            print("   3. إعادة تشغيل VSCode")
            print("   4. إعادة تشغيل الكمبيوتر")
            print("=" * 60)
            input("اضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
