@echo off
chcp 65001 >nul
title Advanced Management System - Safe Launcher v2.0

echo ========================================
echo    Advanced Management System
echo         Safe Launcher v2.0
echo ========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo 💡 Please install Python 3.7 or higher
    echo 🔗 Download from: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo ✅ Python is available
)

REM Check if main.py exists
echo [2/4] Checking program files...
if not exist "main.py" (
    echo ❌ Error: main.py not found
    echo 💡 Please make sure you're in the correct directory
    pause
    exit /b 1
) else (
    echo ✅ main.py found
)

REM Check if ui directory exists
if not exist "ui" (
    echo ❌ Error: ui directory not found
    echo 💡 Please make sure all program files are present
    pause
    exit /b 1
) else (
    echo ✅ ui directory found
)

REM Try to run the safe launcher first
echo [3/4] Attempting safe launch...
if exist "run_program_safe.py" (
    echo 🚀 Using safe launcher...
    python run_program_safe.py
    if not errorlevel 1 (
        echo ✅ Program completed successfully
        goto :end
    )
    echo ⚠️ Safe launcher failed, trying direct launch...
)

REM Fallback to direct launch
echo [4/4] Direct program launch...
echo 🚀 Starting the program directly...
echo.

python main.py

REM Check if the program ran successfully
if errorlevel 1 (
    echo.
    echo ❌ Program ended with an error
    echo 💡 Troubleshooting tips:
    echo    • Check if all required modules are installed
    echo    • Run: pip install -r requirements.txt
    echo    • Restart your computer and try again
    echo    • Check the logs folder for error details
    pause
) else (
    echo.
    echo ✅ Program ended successfully
)

:end
echo.
echo 👋 Thank you for using Advanced Management System
pause
