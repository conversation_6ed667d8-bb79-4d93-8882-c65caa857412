{"version": "0.2.0", "configurations": [{"name": "Python: تشغيل البرنامج الرئيسي", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "justMyCode": false, "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "PYTHONIOENCODING": "utf-8"}, "args": []}, {"name": "Python: الم<PERSON><PERSON> الحالي", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false, "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "PYTHONIOENCODING": "utf-8"}}, {"name": "Python: اختبار جميع الأنظمة", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_all_systems.py", "console": "integratedTerminal", "justMyCode": false, "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "PYTHONIOENCODING": "utf-8"}}, {"name": "Python: تشغ<PERSON><PERSON> آمن", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_safe.py", "console": "integratedTerminal", "justMyCode": false, "stopOnEntry": false, "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "PYTHONIOENCODING": "utf-8"}}]}